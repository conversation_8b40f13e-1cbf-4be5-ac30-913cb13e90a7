const Joi = require('joi');
const {
    objectIdRQSchema,
    booleanRQSchema,
    numberRQSchema,
    stringRQSchema,
    stringSchema,
    numberSchema,
} = require('../utility/validationSchemas');

exports.getCourseWiseFacialDataValidator = Joi.object({
    courseId: objectIdRQSchema,
    programId: objectIdRQSchema,
});

exports.updateCourseWiseFacialSettingValidator = Joi.object({
    courseId: objectIdRQSchema,
    programId: objectIdRQSchema,
    studentFacial: booleanRQSchema,
    staffFacial: booleanRQSchema,
    sessionChange: booleanRQSchema,
    autoEndAttendance: numberRQSchema,
});

exports.getCourseWiseStaffDetailsValidator = Joi.object({
    institutionCalenderId: objectIdRQSchema,
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    yearNo: stringRQSchema,
    levelNo: stringRQSchema,
    term: stringRQSchema,
    rotation: stringRQSchema,
    deliveryType: stringRQSchema,
    gender: stringRQSchema,
    sessionId: Joi.array().items(objectIdRQSchema).optional(),
});

exports.getCourseWiseUserDetailsValidator = Joi.object({
    institutionCalenderId: objectIdRQSchema,
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    yearNo: stringRQSchema,
    levelNo: stringRQSchema,
    term: stringRQSchema,
    rotation: stringRQSchema,
});

exports.updateCourseWiseDeliveryTypeValidator = Joi.object({
    groups: Joi.array()
        .items(
            Joi.object({
                institutionCalenderId: objectIdRQSchema,
                programId: objectIdRQSchema,
                courseId: objectIdRQSchema,
                yearNo: stringRQSchema,
                levelNo: stringRQSchema,
                term: stringRQSchema,
                rotation: stringSchema,
                rotation_count: numberSchema,
                deliveryType: stringRQSchema,
                delivery_symbol: stringRQSchema,
                groupType: stringRQSchema,
                groupNo: stringRQSchema,
                groupId: objectIdRQSchema,
                sessionId: objectIdRQSchema,
                staffMembers: Joi.array().items(objectIdRQSchema).required(),
            }),
        )
        .required(),
});
