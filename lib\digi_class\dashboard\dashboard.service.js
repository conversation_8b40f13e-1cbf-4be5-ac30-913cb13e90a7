const courseSchedules = require('../../models/course_schedule');
const Course = require('../../models/digi_course');
const document_manager = require('../../models/document_manager');
const notification = require('../../models/notification');
const Activities = require('../../models/activities');
const Question = require('../../models/question');
const ProgramCalendar = require('../../models/program_calendar');
const { getUserCourseLists } = require('../course_session/course_session_service');
const { getCoursesForActivity } = require('../document_manager/document_manager_service');
const { convertToMongoObjectId, clone, convertToUtcFormat } = require('../../utility/common');
const InstitutionCalendars = require('../../utility/institution_calendar');
const {
    SCHEDULE_TYPES,
    DC_STUDENT,
    TIME_GROUP_BOOKING_TYPE,
    PRESENT,
    DC_STAFF,
    STARTED,
    SCHEDULE,
    SCHEDULE_TYPES: { EVENT, SUPPORT_SESSION, REGULAR },
    PUBLISHED,
    STUDENT_WARNING_REDIS,
} = require('../../utility/constants');
// const { logger } = require('../../utility/util_keys');
//const { getProgramCalendar } = require('../../../service/cache.service');
const isDeleteActive = {
    isDeleted: false,
    isActive: true,
};
const { getRemoteInfra } = require('../../utility/data.service');
const { dateTimeBasedConverter } = require('../../utility/common_functions');
const { NOT_STARTED } = require('../../utility/enums');
const {
    logger,
    SERVICES: { COURSE_RESTRICTION },
} = require('../../utility/util_keys');
const {
    checkRestrictCourseForDashboard,
    lmsNewSettingWithOptions,
    getWarningDataFromRedis,
} = require('../../utility/utility.service');

const getUniqueStudentGroup = (studentGroups) => {
    if (!studentGroups) {
        return '';
    }
    const studentGroupsArray = studentGroups.split('),');
    const formatStudentGroups = studentGroupsArray
        .filter((groupElement) => groupElement !== '')
        .map((groupElement) => groupElement + ')');
    const uniqueStudentGroups = new Set(formatStudentGroups);
    const uniqueStudentString = Array.from(uniqueStudentGroups).join(',');
    return uniqueStudentString;
};

const getDashboardDocumentList = async (userId, type, _institution_id, courseAdmin) => {
    try {
        const getCourseInfo = await getCoursesForActivity(userId, courseAdmin, _institution_id);
        const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        const DocumentQuery = {
            isDeleted: false,
            _institution_calendar_id: { $in: institutionCalendarIds },
        };
        if (getCourseInfo.length > 0) {
            const getCourseParams = [];
            for (const getCourseParam of getCourseInfo) {
                const courseParam = {
                    _institution_calendar_id: getCourseParam._institution_calendar_id,
                    _program_id: getCourseParam._program_id,
                    term: getCourseParam.term,
                    year_no: getCourseParam.year_no,
                    level_no: getCourseParam.level_no,
                    _course_id: getCourseParam._id,
                    rotation: getCourseParam.rotation,
                };
                if (getCourseParam.rotation_count) {
                    courseParam.rotation_count = getCourseParam.rotation_count;
                }
                getCourseParams.push(courseParam);
            }
            const cQuery = { isDeleted: false };
            cQuery._institution_calendar_id = { $in: institutionCalendarIds };
            if (type === DC_STAFF) {
                cQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            } else {
                cQuery['students._id'] = convertToMongoObjectId(userId);
            }
            cQuery.$or = getCourseParams;
            const courseSchedule = await courseSchedules.find(cQuery, {
                _id: 1,
                _course_id: 1,
                course_name: 1,
                course_code: 1,
                session: 1,
                _institution_calendar_id: 1,
            });

            // const sessionIds = [];
            // for (cSchedule of courseSchedule) {
            //     if (cSchedule.session && cSchedule.session._session_id) {
            //         sessionIds.push({ 'sessionOrScheduleIds._id': cSchedule.session._session_id });
            //     } else {
            //         sessionIds.push({ 'sessionOrScheduleIds._id': cSchedule._id });
            //     }
            // }
            // if (sessionIds.length > 0) {
            //     DocumentQuery.$or = sessionIds;
            // }

            DocumentQuery._course_id = {
                $in: [
                    ...new Set(
                        courseSchedule.map((scheduleElement) =>
                            scheduleElement._course_id.toString(),
                        ),
                    ),
                ],
            };
            const documentProject = {
                type: 1,
                name: 1,
                _course_id: 1,
                courseAdmin: 1,
                url: 1,
            };
            console.time('documentList');
            const documentList = clone(
                await document_manager
                    .find(DocumentQuery, documentProject)
                    .sort({ createdAt: -1 })
                    .limit(5),
            );
            console.timeEnd('documentList');
            for (documentElement of documentList) {
                const courseDetail = getCourseInfo.find(
                    (courseData) =>
                        courseData._id.toString() === documentElement._course_id.toString(),
                );
                documentElement.courseDetail = courseDetail
                    ? {
                          _course_id: courseDetail._id,
                          course_name: courseDetail.course_name,
                          course_code: courseDetail.course_code,
                      }
                    : {};
                documentElement.starred = false;
                documentElement.sessions = [];
            }
            return documentList;
        }
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getDashboardDocumentListOptimized = async (userId, type, institutionCalendarId) => {
    try {
        const userCourseInfo = await getUserCourseLists({
            userId,
            type,
            institutionCalendarId,
        });
        const DocumentQuery = {
            isDeleted: false,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _course_id: {
                $in: [
                    ...new Set(
                        userCourseInfo.map((scheduleElement) => scheduleElement._id.toString()),
                    ),
                ],
            },
        };
        const documentProject = {
            type: 1,
            name: 1,
            _course_id: 1,
            courseAdmin: 1,
            url: 1,
        };
        console.time('documentList');
        const documentList = clone(
            await document_manager
                .find(DocumentQuery, documentProject)
                .sort({ createdAt: -1 })
                .limit(5),
        );
        console.timeEnd('documentList');
        for (documentElement of documentList) {
            const courseDetail = userCourseInfo.find(
                (courseData) => courseData._id.toString() === documentElement._course_id.toString(),
            );
            documentElement.courseDetail = courseDetail
                ? {
                      _course_id: courseDetail._id,
                      course_name: courseDetail.course_name,
                      course_code: courseDetail.course_code,
                  }
                : {};
            documentElement.starred = false;
            documentElement.sessions = [];
        }
        return documentList;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getDashboardCoursesList = async (userId, type, institutionCalendarId) => {
    try {
        // const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        const scheduleQuery =
            type === DC_STUDENT
                ? {
                      'students._id': convertToMongoObjectId(userId),
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      ...isDeleteActive,
                  }
                : {
                      'staffs._staff_id': convertToMongoObjectId(userId),
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      ...isDeleteActive,
                  };
        console.time('ScheduleData');
        const scheduleDatas = await courseSchedules.find(scheduleQuery, {
            _id: 1,
            program_name: 1,
            _program_id: 1,
            _course_id: 1,
            course_name: 1,
            course_code: 1,
            term: 1,
            year_no: 1,
            level_no: 1,
            mode: 1,
            title: 1,
            rotation: 1,
            rotation_count: 1,
            _institution_calendar_id: 1,
        });
        console.timeEnd('ScheduleData');
        const filteredSchedule = scheduleDatas.reduce(
            (unique, item) =>
                unique.find(
                    (ele) =>
                        ele._course_id.toString() === item._course_id.toString() &&
                        ele.term.toString() === item.term.toString() &&
                        ele.level_no.toString() === item.level_no.toString() &&
                        ele._program_id.toString() === item._program_id.toString() &&
                        ((ele.rotation.toString() === 'yes' &&
                            ele.rotation_count &&
                            item.rotation_count &&
                            ele.rotation_count.toString() === item.rotation_count.toString()) ||
                            ele.rotation.toString() === 'no'),
                )
                    ? unique
                    : [...unique, item],
            [],
        );
        /* TODO replace to cache service to db query */
        //let pcCourseData = await getProgramCalendar(institutionCalendarId);
        const pcCourseData = await ProgramCalendar.find({
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            isDeleted: false,
            status: PUBLISHED,
        }).lean();
        const pcCourses = [];
        for (programCalendarElement of pcCourseData) {
            for (levelElement of programCalendarElement.level) {
                if (levelElement.rotation === 'no')
                    for (courseElement of levelElement.course) {
                        pcCourses.push({
                            _program_id: programCalendarElement._program_id.toString(),
                            term: levelElement.term,
                            year: levelElement.year,
                            level: levelElement.level_no,
                            rotation: levelElement.rotation,
                            _id: courseElement._course_id,
                            _course_id: courseElement._course_id,
                            course_name: courseElement.courses_name,
                            course_number: courseElement.courses_number,
                            course_type: courseElement.model,
                            start_date: courseElement.start_date,
                            end_date: courseElement.end_date,
                            _institution_calendar_id:
                                programCalendarElement._institution_calendar_id,
                        });
                    }
                else
                    for (rotationCourseElement of levelElement.rotation_course) {
                        for (courseElement of rotationCourseElement.course) {
                            pcCourses.push({
                                _program_id: programCalendarElement._program_id.toString(),
                                term: levelElement.term,
                                year: levelElement.year,
                                level: levelElement.level_no,
                                rotation: levelElement.rotation,
                                rotation_count: rotationCourseElement.rotation_count,
                                _id: courseElement._course_id,
                                _course_id: courseElement._course_id,
                                course_name: courseElement.courses_name,
                                course_number: courseElement.courses_number,
                                course_type: courseElement.model,
                                start_date: courseElement.start_date,
                                end_date: courseElement.end_date,
                                _institution_calendar_id:
                                    programCalendarElement._institution_calendar_id,
                            });
                        }
                    }
            }
        }
        const courses = pcCourses.filter((calendarCourse) => {
            const loc = filteredSchedule.findIndex(
                (scheduleCourse) =>
                    scheduleCourse._course_id.toString() === calendarCourse._id.toString() &&
                    scheduleCourse._program_id.toString() ===
                        calendarCourse._program_id.toString() &&
                    scheduleCourse.term.toString() === calendarCourse.term.toString() &&
                    scheduleCourse.level_no.toString() === calendarCourse.level.toString() &&
                    ((scheduleCourse.rotation.toString() === 'yes' &&
                        scheduleCourse.rotation_count &&
                        calendarCourse.rotation_count &&
                        scheduleCourse.rotation_count.toString() ===
                            calendarCourse.rotation_count.toString()) ||
                        scheduleCourse.rotation.toString() === 'no'),
            );
            if (loc !== -1) {
                calendarCourse.program_name = filteredSchedule[loc].program_name;
                return true;
            }
            return false;
        });
        const courseIds = courses.map((course) => course._course_id);
        const CourseLabels = await Course.find({ _id: { $in: courseIds } }).lean();
        for (course of courses) {
            const label = CourseLabels.find(
                (courseLabel) => courseLabel._id.toString() === course._course_id.toString(),
            );
            if (label) {
                course.course_name_labels = label.course_name_labels || null;
                course.course_code_labels = label.course_code_labels || null;
                course.versionNo = label.versionNo || 1;
                course.versioned = label.versioned || false;
                course.versionName = label.versionName || '';
                course.versionedFrom = label.versionedFrom || null;
                course.versionedCourseIds = label.versionedCourseIds || [];
            }
        }
        return courses;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getDashboardSessionsList = async (userId, type, _institution_id, dateTime, timeZone) => {
    try {
        const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        const timeZonedStartDate = dateTimeBasedConverter(dateTime, timeZone);
        const timeZonedBeginDate = timeZonedStartDate.clone().startOf('day');
        const timeZonedEndDate = timeZonedStartDate.clone().endOf('day');
        // console.log('TO local ', timeZonedStartDate, new Date(timeZonedStartDate));
        // console.log('TO local Start ', timeZonedBeginDate, new Date(timeZonedBeginDate));
        // console.log('TO local End ', timeZonedEndDate, new Date(timeZonedEndDate));
        // const startDateTime = new Date(dateTime);
        // const todayDateStart = new Date(dateTime);
        // todayDateStart.setHours(0, 0, 0, 0);
        // const todayDateEnd = new Date(dateTime);
        // todayDateEnd.setHours(23, 59, 59, 59);
        const scheduleQuery =
            type === DC_STUDENT
                ? {
                      'students._id': convertToMongoObjectId(userId),
                      _institution_calendar_id: { $in: institutionCalendarIds },
                      ...isDeleteActive,
                  }
                : {
                      $or: [
                          { 'staffs._staff_id': convertToMongoObjectId(userId) },
                          { 'attendanceTakingStaff.staffId': convertToMongoObjectId(userId) },
                      ],
                      _institution_calendar_id: { $in: institutionCalendarIds },
                      ...isDeleteActive,
                  };
        if (scheduleQuery.$or) {
            scheduleQuery.$and = [
                {
                    $or: [
                        {
                            scheduleStartDateAndTime: {
                                $gte: new Date(timeZonedBeginDate),
                                $lte: new Date(timeZonedEndDate),
                            },
                        },
                        {
                            scheduleEndDateAndTime: {
                                $gte: new Date(timeZonedStartDate),
                                $lte: new Date(timeZonedEndDate),
                            },
                        },
                    ],
                },
            ];
        } else {
            scheduleQuery.$or = [
                {
                    scheduleStartDateAndTime: {
                        $gte: new Date(timeZonedBeginDate),
                        $lte: new Date(timeZonedEndDate),
                    },
                },
                {
                    scheduleEndDateAndTime: {
                        $gte: new Date(timeZonedStartDate),
                        $lte: new Date(timeZonedEndDate),
                    },
                },
            ];
        }

        // scheduleQuery.scheduleEndDateAndTime = { $gte: startDateTime, $lte: todayDateEnd };
        scheduleQuery.status = { $nin: ['completed', 'missed'] };
        // logger.info(scheduleQuery, 'Session log check');
        let scheduleDatas = await courseSchedules
            .find(scheduleQuery, {
                attendanceTakingStaff: 1,
                _id: 1,
                program_name: 1,
                _program_id: 1,
                _course_id: 1,
                course_name: 1,
                course_code: 1,
                term: 1,
                year_no: 1,
                level_no: 1,
                mode: 1,
                title: 1,
                rotation: 1,
                rotation_count: 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
                type: 1,
                name: 1,
                status: 1,
                subjects: 1,
                merge_status: 1,
                session: 1,
                merge_with: 1,
                infra_name: 1,
                infra_id: '$_infra_id',
                sub_type: 1,
                staffs: 1,
                sessionDetail: 1,
                socket_port: 1,
                students: 1,
                retakeStatus: 1,
                _institution_calendar_id: 1,
                uuid: 1,
                isLive: 1,
                classModeType: 1,
                scheduleStartFrom: 1,
            })
            .populate({
                path: '_infra_id',
                select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
            })
            .populate({
                path: '_course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        // return scheduleDatas;
        const remoteInfra = await getRemoteInfra();
        const mergeSessionIds = [];
        const warningKeys = new Map();
        let warningConfig = [];
        if (type === DC_STUDENT && scheduleDatas.length && COURSE_RESTRICTION === 'true') {
            ({ warningConfig } = await lmsNewSettingWithOptions({
                _institution_id,
                _institution_calendar_id: scheduleDatas[0]?._institution_calendar_id,
                project: {
                    'warningConfig._id': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.restrictCourseAccess': 1,
                    // 'warningConfig.notificationToStudent.setType': 1,
                },
                sort: true,
            }));
        }
        scheduleDatas = scheduleDatas.map((courseSchedule) => {
            if (!mergeSessionIds.find((ele) => ele.toString() === courseSchedule._id.toString())) {
                const { _infra_id } = courseSchedule;
                if (_infra_id) {
                    let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                    if (_infra_id.zone.length) {
                        infraName += ',' + _infra_id.zone.toString();
                    }
                    infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                    courseSchedule.infra_name = infraName;
                }
                if (courseSchedule.mode === TIME_GROUP_BOOKING_TYPE.REMOTE) {
                    courseSchedule.infraDatas = remoteInfra.find(
                        (infraElement) =>
                            courseSchedule.infra_id &&
                            infraElement._id.toString() === courseSchedule.infra_id.toString(),
                    );
                }
                if (courseSchedule.merge_status) {
                    const mergedSessions = courseSchedule.merge_with.map((mergeWith) => {
                        const sessionDetails = scheduleDatas.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                    mergeWith.schedule_id.toString() &&
                                courseScheduleEntry.session &&
                                courseScheduleEntry.session._session_id.toString() ===
                                    mergeWith.session_id.toString(),
                        );
                        if (sessionDetails) {
                            mergeSessionIds.push(sessionDetails._id.toString());
                            mergeWith.session = {
                                _session_id: sessionDetails.session._session_id,
                                s_no: sessionDetails.session.s_no,
                                delivery_symbol: sessionDetails.session.delivery_symbol,
                                delivery_no: sessionDetails.session.delivery_no,
                            };
                        }
                        return mergeWith;
                    });
                    courseSchedule.merge_with = mergedSessions;
                }
                if (type === DC_STAFF)
                    courseSchedule.staffDetails = courseSchedule.staffs.find(
                        (staffElement) => staffElement._staff_id.toString() === userId.toString(),
                    );
                else
                    courseSchedule.studentDetails = courseSchedule.students.find(
                        (staffElement) => staffElement._id.toString() === userId.toString(),
                    );
                // delete courseSchedule.staffs;
                courseSchedule.versionNo = courseSchedule._course_id.versionNo || 1;
                courseSchedule.versioned = courseSchedule._course_id.versioned || false;
                courseSchedule.versionName = courseSchedule._course_id.versionName || '';
                courseSchedule.versionedFrom = courseSchedule._course_id.versionedFrom || null;
                courseSchedule.versionedCourseIds =
                    courseSchedule._course_id.versionedCourseIds || [];
                courseSchedule._course_id = courseSchedule._course_id._id;
                delete courseSchedule.students;

                // push keys to check  multi course warnings without loop
                warningKeys.set(
                    `${STUDENT_WARNING_REDIS}:${courseSchedule._institution_calendar_id?.toString()}_${courseSchedule._program_id.toString()}_${courseSchedule._course_id.toString()}_${
                        courseSchedule.year_no
                    }_${courseSchedule.level_no}_${courseSchedule.term}_${
                        courseSchedule.rotation_count ? courseSchedule.rotation_count : null
                    }`,
                    0,
                );
                console.log({
                    courseSchedule,
                });
                const projection =
                    type === DC_STAFF
                        ? { 'settings.staffFacial': 1 }
                        : type === DC_STUDENT
                        ? { 'settings.studentFacial': 1 }
                        : {};
                const courseFacialData = await Course.findOne({
                    _id: convertToMongoObjectId(
                        courseSchedule._course_id || courseSchedule._course_id._id,
                    ),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(courseSchedule._program_id),
                    isDeleted: false,
                    isActive: true,
                }, projection).lean();
                const faceAuthenticationValue =
                    (type === DC_STAFF && courseFacialData?.settings?.staffFacial) ||
                    (type === DC_STUDENT && courseFacialData?.settings?.studentFacial) ||
                    false;

                return { ...courseSchedule, faceAuthenticationValue };
            }
            return false;
        });
        let overallWarningList = new Map();
        if (
            type === DC_STUDENT &&
            COURSE_RESTRICTION === 'true' &&
            warningConfig.length &&
            warningConfig[0]?.restrictCourseAccess
        ) {
            overallWarningList = scheduleDatas.length
                ? await getWarningDataFromRedis(warningKeys)
                : new Map();
        }
        scheduleDatas = scheduleDatas.filter(
            (scheduleElement) => {
                if (
                    scheduleElement &&
                    type === DC_STUDENT &&
                    COURSE_RESTRICTION === 'true' &&
                    warningConfig.length &&
                    warningConfig[0]?.restrictCourseAccess
                ) {
                    const key = `${STUDENT_WARNING_REDIS}:${scheduleElement._institution_calendar_id?.toString()}_${scheduleElement._program_id.toString()}_${scheduleElement._course_id.toString()}_${
                        scheduleElement.year_no
                    }_${scheduleElement.level_no}_${scheduleElement.term}_${
                        scheduleElement.rotation_count ? scheduleElement.rotation_count : null
                    }`;

                    if (overallWarningList.has(key)) {
                        const warnings = overallWarningList.get(key);
                        restrictedStudents = warnings[warningConfig[0]?._id.toString()] ?? [];
                        if (restrictedStudents.includes(userId.toString())) {
                            scheduleElement.restrictCourseAccess =
                                warningConfig[0]?.restrictCourseAccess;
                        }
                    }
                }
                return scheduleElement !== false;
            },
            // !mergeSessionIds.find((ele) => ele.toString() === scheduleElement._id.toString()),
        );

        const today = new Date(timeZonedStartDate);
        const time = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate();
        const startDateAndTimeEntry = new Date(time);
        const endToday = new Date(timeZonedEndDate);
        const endDate =
            endToday.getFullYear() + '-' + (endToday.getMonth() + 1) + '-' + endToday.getDate();
        const endDateTime = new Date(endDate).setHours(23, 59, 59, 0);
        const endDateAndTimeEntry = new Date(endDateTime);
        // Adding today Activity
        const activities = await Activities.find({
            'schedule.startDateAndTime': {
                $gte: new Date(startDateAndTimeEntry),
                $lte: new Date(endDateAndTimeEntry),
            },
            isDeleted: false,
            status: { $in: ['not_started', STARTED] },
            type: SCHEDULE,
        });
        if (activities.length) {
            const activityDetails = [];
            const activityScheduleIds = activities
                .map((activityElement) => activityElement.scheduleIds)
                .flat();
            const activitySchedules = await courseSchedules
                .find(
                    {
                        _id: { $in: activityScheduleIds },
                        $or: [
                            {
                                'staffs._staff_id': convertToMongoObjectId(userId),
                            },
                            {
                                'students._id': convertToMongoObjectId(userId),
                            },
                        ],
                    },
                    {},
                )
                .populate({
                    path: '_course_id',
                    select: {
                        versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedFrom: 1,
                        versionedCourseIds: 1,
                    },
                });
            for (const activity of activities) {
                const { scheduleIds, sessionId, sessionFlowIds /* questions, students */ } =
                    activity;
                const courseScheduleData = activitySchedules.filter((scheduleElement) =>
                    scheduleIds.find(
                        (activityScheduleElement) =>
                            activityScheduleElement.toString() === scheduleElement._id.toString(),
                    ),
                );
                if (courseScheduleData.length) {
                    let studentGroupsName = '';
                    for (const scheduleId of courseScheduleData) {
                        const { merge_status, merge_with } = scheduleId;
                        if (merge_status) {
                            const scheduleIds = merge_with.map((mergeWith) =>
                                convertToMongoObjectId(mergeWith.schedule_id),
                            );
                            if (scheduleIds.length) {
                                const mergedSchedules = await courseSchedules.find({
                                    _id: { $in: scheduleIds },
                                });
                                mergedSchedules.push(scheduleId);
                                mergedSchedules.forEach((courseSchedule) => {
                                    const { student_groups, _id } = courseSchedule;
                                    if (student_groups && student_groups.length) {
                                        let studentGroups = student_groups.map((student_group) => {
                                            const { group_name, session_group } = student_group;
                                            let groupName = group_name.split('-').slice(-2);
                                            groupName = groupName[1]
                                                ? groupName[0] + '-' + groupName[1]
                                                : groupName[0];
                                            if (session_group && session_group.length) {
                                                let sessionGroup = session_group.map(
                                                    (groupNameEntry) => {
                                                        let groupNames = groupNameEntry.group_name
                                                            .split('-')
                                                            .slice(-2);
                                                        groupNames = groupNames[1]
                                                            ? groupNames[0] + '-' + groupNames[1]
                                                            : groupNames[0];
                                                        return groupNames;
                                                    },
                                                );
                                                sessionGroup = sessionGroup.toString();
                                                groupName += '(' + sessionGroup + ')';
                                            }
                                            return groupName;
                                        });
                                        studentGroups = studentGroups.toString();
                                        studentGroupsName += studentGroups + ',';
                                    }
                                });
                            }
                        } else {
                            const { student_groups } = scheduleId;
                            if (student_groups && student_groups.length) {
                                let studentGroups = student_groups.map((student_group) => {
                                    const { group_name, session_group } = student_group;
                                    let groupName = group_name.split('-').slice(-2);
                                    groupName = groupName[1]
                                        ? groupName[0] + '-' + groupName[1]
                                        : groupName[0];
                                    if (session_group && session_group.length) {
                                        let sessionGroup = session_group.map((groupNameEntry) => {
                                            let groupNames = groupNameEntry.group_name
                                                .split('-')
                                                .slice(-2);
                                            groupNames = groupNames[1]
                                                ? groupNames[0] + '-' + groupNames[1]
                                                : groupNames[0];
                                            return groupNames;
                                        });
                                        sessionGroup = sessionGroup.toString();
                                        groupName += '(' + sessionGroup + ')';
                                    }
                                    return groupName;
                                });
                                studentGroups = studentGroups.toString();
                                studentGroupsName += studentGroups + ',';
                            }
                        }
                    }
                    // let sessionIds = sessionFlowIds.map((sessionFlowId) =>
                    //     convertToMongoObjectId(sessionFlowId._id),
                    // );
                    // const sessions = await courseSchedules
                    //     .find(
                    //         {
                    //             $or: [
                    //                 {
                    //                     'session._session_id': { $in: sessionIds },
                    //                 },
                    //                 {
                    //                     _id: { $in: sessionIds },
                    //                 },
                    //             ],
                    //         },
                    //         { session: 1, title: 1, type: 1, _id: 1 },
                    //     )
                    //     .lean();
                    // sessionIds = sessionIds.map((sessionFlow) => sessionFlow.toString());
                    // const sessionDetails = sessionFlowIds.map((sessionId) => {
                    //     const sessionDetail = sessions.find(
                    //         (sessionEntry) =>
                    //             (sessionEntry.session &&
                    //                 sessionEntry.session._session_id.toString() ===
                    //                     sessionId._id.toString()) ||
                    //             (sessionEntry.type === sessionId.type &&
                    //                 sessionEntry._id.toString() === sessionId._id.toString()),
                    //     );
                    //     if (sessionDetail) {
                    //         const { session, _id, title, type } = sessionDetail;
                    //         if (session) {
                    //             const { _session_id } = session;
                    //             if (
                    //                 session &&
                    //                 _session_id &&
                    //                 sessionIds.includes(_session_id.toString())
                    //             ) {
                    //                 return {
                    //                     _id: session._session_id,
                    //                     s_no: session.s_no,
                    //                     delivery_symbol: session.delivery_symbol,
                    //                     delivery_no: session.delivery_no,
                    //                     session_type: session.session_type,
                    //                     session_topic: session.session_topic,
                    //                     type: REGULAR,
                    //                 };
                    //             }
                    //         }
                    //         if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                    //             return { _id, title, type };
                    //         }
                    //     }
                    // });
                    const schedule = courseScheduleData.find(
                        (scheduleId) =>
                            scheduleId.staffs.find(
                                (staff) => staff._staff_id.toString() === userId.toString(),
                            ) ||
                            scheduleId.students.find(
                                (student) => student._id.toString() === userId.toString(),
                            ),
                    );

                    activityDetails.push({
                        activityId: activity._id,
                        status: activity.status,
                        name: activity.name,
                        quizType: activity.quizType,
                        schedule: activity.schedule,
                        questionsCount: activity.questions.length,
                        socketEventStaffId: activity.socketEventStaffId,
                        staffStartWithExam: activity.staffStartWithExam,
                        socketEventStudentId: activity.socketEventStudentId,
                        _institution_calendar_id: activity._institution_calendar_id,
                        // totalStudentAnsweredCount: students ? students.length : 0,
                        // totalStudentCount: schedule && schedule.students ? schedule.students.length : 0,
                        createdBy: activity.createdBy,
                        setQuizTime: activity.setQuizTime,
                        type: 'activity',
                        studentGroupName: getUniqueStudentGroup(studentGroupsName),
                        sessionId,
                        sessionType: schedule && schedule.type ? schedule.type : undefined,
                        // _institution_calendar_id:
                        //     schedule && schedule._institution_calendar_id
                        //         ? schedule._institution_calendar_id
                        //         : undefined,
                        year_no: schedule && schedule.year_no ? schedule.year_no : undefined,
                        level_no: schedule && schedule.level_no ? schedule.level_no : undefined,
                        term: schedule && schedule.term ? schedule.term : undefined,
                        _program_id:
                            schedule && schedule._program_id ? schedule._program_id : undefined,
                        _course_id:
                            schedule && schedule._course_id && schedule._course_id._id
                                ? schedule._course_id._id
                                : undefined,
                        versionNo:
                            schedule && schedule._course_id && schedule._course_id.versionNo
                                ? schedule._course_id.versionNo
                                : 1,
                        versioned:
                            schedule && schedule._course_id && schedule._course_id.versioned
                                ? schedule._course_id.versioned
                                : false,
                        versionName:
                            schedule && schedule._course_id && schedule._course_id.versionName
                                ? schedule._course_id.versionName
                                : '',
                        versionedFrom:
                            schedule && schedule._course_id && schedule._course_id.versionedFrom
                                ? schedule._course_id.versionedFrom
                                : null,
                        versionedCourseIds:
                            schedule &&
                            schedule._course_id &&
                            schedule._course_id.versionedCourseIds
                                ? schedule._course_id.versionedCourseIds
                                : [],
                        course_name:
                            schedule && schedule.course_name ? schedule.course_name : undefined,
                        merge_status: schedule ? schedule.merge_status : undefined,
                        _id: schedule && schedule._id ? schedule._id : undefined,
                        // sessionFlowIds: sessionDetails,
                        rotation: schedule && schedule.rotation ? schedule.rotation : undefined,
                        rotation_count:
                            schedule && schedule.rotation_count
                                ? schedule.rotation_count
                                : undefined,
                        start_date: activity.schedule.startDateAndTime,
                        end_date: activity.schedule.endDateAndTime,
                        isNewActivity: !!(
                            activity.correctionType && activity.correctionType !== ''
                        ),
                    });
                }
            }
            scheduleDatas = scheduleDatas.concat(activityDetails);
        }
        return scheduleDatas.sort((a, b) => {
            if (a.start_date && b.start_date)
                return new Date(a.start_date) - new Date(b.start_date);
            return new Date(a.scheduleStartDateAndTime) - new Date(b.scheduleStartDateAndTime);
        });
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getDashboardRating = async (userId, _institution_id) => {
    try {
        const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        const startCurrentTime = new Date();
        startCurrentTime.setHours(0);
        startCurrentTime.setMinutes(0);
        startCurrentTime.setSeconds(0);
        startCurrentTime.setMilliseconds(0);
        const endCurrentTime = new Date();
        endCurrentTime.setHours(23);
        endCurrentTime.setMinutes(59);
        endCurrentTime.setSeconds(0);
        endCurrentTime.setMilliseconds(0);
        //dead code - notification for offline logic to be moved to sqs
        // const notificationCount = await notification
        //     .find({
        //         institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        //         users: {
        //             $elemMatch: {
        //                 _id: convertToMongoObjectId(userId),
        //                 isViewed: false,
        //             },
        //         },
        //         $and: [
        //             { createdAt: { $gt: startCurrentTime } },
        //             { createdAt: { $lt: endCurrentTime } },
        //         ],
        //         isDeleted: false,
        //     })
        //     .countDocuments();

        const scheduleQuery = {
            staffs: {
                $elemMatch: {
                    _staff_id: convertToMongoObjectId(userId),
                    status: PRESENT,
                },
            },
            _institution_calendar_id: { $in: institutionCalendarIds },
            students: { $exists: true, $type: 'array', $ne: [] },
            'session._session_id': { $exists: true, $ne: null },
            status: 'completed',
            ...isDeleteActive,
        };
        const scheduleDatas = await courseSchedules
            .find(scheduleQuery, {
                'students.status': 1,
                'students.feedBack': 1,
                merge_status: 1,
                merge_with: 1,
                _id: 1,
                _institution_calendar_id: 1,
            })
            .lean();
        let sumOfRatings = 0;
        let count = 0;
        const scheduleIds = [];
        for (scheduleElement of scheduleDatas) {
            const studentElement = scheduleElement.students.filter(
                (student) =>
                    /*  student.status === PRESENT && */
                    student.feedBack && student.feedBack.rating && student.feedBack.rating !== 0,
            );
            sumOfRatings += studentElement
                .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                .reduce((a, b) => a + b, 0);
            if (studentElement.length !== 0) {
                count += studentElement.length;
                scheduleIds.push(scheduleElement._id.toString());
                if (scheduleElement.merge_status) {
                    for (mergeScheduleIds of scheduleElement.merge_with) {
                        scheduleIds.push(mergeScheduleIds.schedule_id.toString());
                    }
                }
            }
        }
        const sessionIdsCount = [...new Set(scheduleIds)].length;
        return {
            averageRating: {
                totalFeedback: count,
                avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                sessionCount: sessionIdsCount,
            },
            notificationCount: 0,
        };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

module.exports = {
    getDashboardDocumentList,
    getDashboardCoursesList,
    getDashboardSessionsList,
    getDashboardRating,
    getDashboardDocumentListOptimized,
    getUniqueStudentGroup,
};
