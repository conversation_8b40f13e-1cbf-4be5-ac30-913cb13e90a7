const {
    DIGI_COURSE,
    COURSE_SCHEDULE,
    USER,
    COMPLETED,
    SCHEDULE_TYPES: { REGULAR },
} = require('../utility/constants');
const courseModal = require('mongoose').model(DIGI_COURSE);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const userSchemas = require('mongoose').model(USER);
const courseWiseSettingSchema = require('./courseWiseSetting.Schema');
const { convertToMongoObjectId } = require('../utility/common');

const updateCourseWiseFacialSettings = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { studentFacial, staffFacial, sessionChange, autoEndAttendance, courseId } = body;
        const result = await courseModal.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(courseId),
            },
            {
                $set: {
                    'settings.studentFacial': studentFacial,
                    'settings.staffFacial': staffFacial,
                    'settings.sessionChange': sessionChange,
                    'settings.autoEndAttendance': autoEndAttendance,
                    'settings.updatedBy': convertToMongoObjectId(_user_id),
                },
            },
        );

        const action = result.upsertedCount > 0 ? 'created' : 'updated';

        return {
            statusCode: 200,
            message: `Facial settings ${action} successfully`,
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseFacialData = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { programId, courseId } = query;

        const courseWiseData = await courseModal
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    'settings.updatedBy': convertToMongoObjectId(_user_id),
                },
                {
                    _id: 1,
                    _program_id: 1,
                    settings: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseWiseData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseUserDetails = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_calendar_id } = headers;
        const { programId, courseId, yearNo, levelNo, term, rotation, rotation_count } = query;

        const courseWiseData = await courseScheduleSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    term,
                    year_no: yearNo,
                    level_no: levelNo,
                    type: REGULAR,
                    ...(rotation === 'yes' && {
                        rotation_count,
                    }),
                    isDeleted: false,
                },
                {
                    'staffs._staff_id': 1,
                },
            )
            .lean();

        const staffIds = new Set();
        courseWiseData?.forEach((scheduleElement) => {
            scheduleElement?.staffs?.forEach((staffElement) => {
                staffIds.add(staffElement._staff_id.toString());
            });
        });

        const uniqueStaffIds = Array.from(staffIds);
        const userData = await userSchemas
            .find(
                {
                    _id: uniqueStaffIds,
                    isDeleted: false,
                    isActive: true,
                    status: COMPLETED,
                },
                { name: 1, user_id: 1, gender: 1 },
            )
            .lean();

        return { statusCode: 200, message: 'DATA_RETRIEVED', data: userData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const updateCourseWiseDeliveryType = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { groups } = body;
        const bulkWriteGroups = [];
        for (const groupElement of groups) {
            const {
                institutionCalenderId,
                programId,
                courseId,
                yearNo,
                levelNo,
                term,
                rotation = 'no',
                rotation_count = null,
                deliveryType,
                delivery_symbol,
                groupType,
                groupNo,
                sessionId,
                staffMembers = [],
                groupId,
            } = groupElement;
            bulkWriteGroups.push({
                updateOne: {
                    filter: {
                        institutionCalendarId: convertToMongoObjectId(institutionCalenderId),
                        programId: convertToMongoObjectId(programId),
                        courseId: convertToMongoObjectId(courseId),
                        term,
                        yearNo,
                        levelNo,
                        groupName: groupNo,
                        gender: groupType,
                        ...(rotation === 'yes' && rotation_count && { rotation_count }),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        groupId,
                    },
                    update: {
                        ...(deliveryType && { deliveryType }),
                        ...(delivery_symbol && { delivery_symbol }),
                        ...(groupNo && { groupName: groupNo }),
                        ...(groupType && { gender: groupType }),
                        ...(groupId && { groupId }),
                        ...(sessionId && { sessionId: convertToMongoObjectId(sessionId) }),
                        ...(courseId && { courseId: convertToMongoObjectId(courseId) }),
                        ...(staffMembers?.length && {
                            userIds: staffMembers.map((staffId) => convertToMongoObjectId(staffId)),
                        }),
                    },
                    upsert: true,
                },
            });
        }

        const updatedGroups = await courseWiseSettingSchema.bulkWrite(bulkWriteGroups);
        if (updatedGroups.upsertedCount === 0 && updatedGroups.modifiedCount === 0) {
            return {
                statusCode: 410,
                message: 'CourseWise Delivery Types Update Failed',
            };
        }

        return {
            statusCode: 200,
            message: 'Course-wise delivery types processed successfully',
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseStaffDetails = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalenderId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation = 'no',
            rotation_count = null,
            deliveryType,
            gender,
            sessionId,
            groupId,
        } = query;

        const queryFilter = {
            _institution_id,
            institutionCalendarId: convertToMongoObjectId(institutionCalenderId),
            programId: convertToMongoObjectId(programId),
            courseId: convertToMongoObjectId(courseId),
            term,
            yearNo,
            levelNo,
            deliveryType,
            gender,
            ...(rotation === 'yes' && { rotation_count }),
        };

        if (sessionId) {
            queryFilter.sessionId = {
                $in: sessionId.map((id) => convertToMongoObjectId(id)),
            };
        }

        const deliverySettings = await courseWiseSettingSchema
            .find(queryFilter, {
                userIds: 1,
                sessionId: 1,
            })
            .lean();
        const staffIds = new Set();
        deliverySettings.forEach((settingElement) => {
            if (settingElement.userIds && Array.isArray(settingElement.userIds)) {
                settingElement.userIds.forEach((staffIdElement) => {
                    staffIds.add(staffIdElement.toString());
                });
            }
        });
        const userData = await userSchemas
            .find(
                {
                    _id: Array.from(staffIds),
                    isDeleted: false,
                    isActive: true,
                    status: COMPLETED,
                },
                { name: 1, user_id: 1, gender: 1, email: 1 },
            )
            .lean();

        const userMap = new Map();
        userData.forEach((userElement) => {
            userMap.set(userElement._id.toString(), userElement);
        });

        const populatedDeliverySettings = deliverySettings.map((settingElement) => {
            const populatedSetting = { ...settingElement };

            if (settingElement.userIds && Array.isArray(settingElement.userIds)) {
                populatedSetting.staff_details = settingElement.userIds
                    .map((staffId) => {
                        const user = userMap.get(staffId.toString());
                        return user
                            ? {
                                  staff_id: user._id,
                                  name: user.name,
                                  user_id: user.user_id,
                                  gender: user.gender,
                                  email: user.email,
                                  sessionId: settingElement.sessionId,
                              }
                            : null;
                    })
                    .filter((staff) => staff !== null);
            } else {
                populatedSetting.staff_details = [];
            }

            return populatedSetting;
        });

        return {
            statusCode: 200,
            message: 'Course delivery group admin data retrieved successfully',
            data: populatedDeliverySettings,
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

module.exports = {
    updateCourseWiseFacialSettings,
    getCourseWiseFacialData,
    getCourseWiseUserDetails,
    updateCourseWiseDeliveryType,
    getCourseWiseStaffDetails,
};
